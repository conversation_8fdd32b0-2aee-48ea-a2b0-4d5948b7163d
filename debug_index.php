<?php
define('ALLOWED_ACCESS', true);
require_once 'includes/config.php';

echo "Testing Services class...\n";
try {
    require_once 'includes/Services.php';
    $servicesHandler = new Services();
    $featuredServices = array_slice($servicesHandler->getAllActiveServices(), 0, 3);
    echo "Services loaded successfully: " . count($featuredServices) . " services\n";
} catch (Exception $e) {
    echo "Services error: " . $e->getMessage() . "\n";
}

echo "\nTesting Partners class...\n";
try {
    require_once 'includes/Partners.php';
    $partnersHandler = new Partners();
    $partners = $partnersHandler->getActivePartners();
    echo "Partners loaded successfully: " . count($partners) . " partners\n";
} catch (Exception $e) {
    echo "Partners error: " . $e->getMessage() . "\n";
}

echo "\nAll tests completed!\n";
?>
